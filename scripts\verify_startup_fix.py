#!/usr/bin/env python3
"""
主节点启动修复验证脚本
用于验证规则模板生成问题的修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from models.database import RuleFieldMetadata, RuleTemplate
from services.rule_change_detector import RuleChangeDetector
from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer
from tools.field_mapping_manager import FieldMappingManager


def verify_field_mapping_config():
    """验证字段映射配置"""
    logger.info("=== 验证字段映射配置 ===")

    try:
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")

        # 检查配置版本
        version = field_mapping_manager.config.get("metadata", {}).get("version", "unknown")
        logger.info(f"字段映射配置版本: {version}")

        # 检查规则类型映射
        rule_type_mappings = field_mapping_manager.config.get("rule_type_mappings", {})
        logger.info(f"规则类型映射数量: {len(rule_type_mappings)}")

        # 检查字段定义
        field_definitions = field_mapping_manager.config.get("field_definitions", {})
        common_fields = field_definitions.get("common_fields", {})
        specific_fields = field_definitions.get("specific_fields", {})
        logger.info(f"通用字段数量: {len(common_fields)}")
        logger.info(f"特定字段数量: {len(specific_fields)}")

        logger.success("字段映射配置验证通过")
        return True

    except Exception as e:
        logger.error(f"字段映射配置验证失败: {e}")
        return False


def verify_rule_files():
    """验证规则文件"""
    logger.info("=== 验证规则文件 ===")

    rules_dir = Path("rules/base_rules")
    if not rules_dir.exists():
        logger.error(f"规则目录不存在: {rules_dir}")
        return False

    rule_files = list(rules_dir.glob("*.py"))
    rule_files = [f for f in rule_files if not f.name.startswith("__")]

    logger.info(f"发现规则文件数量: {len(rule_files)}")
    for rule_file in rule_files:
        logger.debug(f"规则文件: {rule_file.name}")

    return len(rule_files) > 0


def verify_database_tables():
    """验证数据库表结构"""
    logger.info("=== 验证数据库表结构 ===")

    try:
        session_factory = get_session_factory()
        with session_factory() as session:
            # 检查 rule_template 表
            template_count = session.query(RuleTemplate).count()
            logger.info(f"rule_template 表记录数: {template_count}")

            # 检查 rule_field_metadata 表
            metadata_count = session.query(RuleFieldMetadata).count()
            logger.info(f"rule_field_metadata 表记录数: {metadata_count}")

            # 检查各状态的模板数量
            from models.database import RuleTemplateStatusEnum

            for status in RuleTemplateStatusEnum:
                count = session.query(RuleTemplate).filter(RuleTemplate.status == status).count()
                logger.info(f"状态 {status.value} 的模板数量: {count}")

        logger.success("数据库表结构验证通过")
        return True

    except Exception as e:
        logger.error(f"数据库表结构验证失败: {e}")
        return False


def run_rule_change_detection():
    """运行规则变更检测"""
    logger.info("=== 运行规则变更检测 ===")

    try:
        session_factory = get_session_factory()
        with session_factory() as session:
            detector = RuleChangeDetector(db_session=session)
            detector.run()

        logger.success("规则变更检测完成")
        return True

    except Exception as e:
        logger.error(f"规则变更检测失败: {e}")
        return False


def test_rule_field_extraction():
    """测试从规则类中提取字段信息"""
    logger.info("=== 测试规则字段提取 ===")

    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)

        # 测试从规则类中提取字段映射
        extracted_mappings = metadata_initializer._extract_rule_mappings_from_classes()

        logger.info(f"从规则类中提取了 {len(extracted_mappings)} 个规则映射")

        for rule_key, mapping in extracted_mappings.items():
            required_count = len(mapping.get("required_fields", []))
            optional_count = len(mapping.get("optional_fields", []))
            logger.debug(f"规则 {rule_key}: 必填字段={required_count}, 可选字段={optional_count}")

        return len(extracted_mappings) > 0

    except Exception as e:
        logger.error(f"规则字段提取测试失败: {e}")
        return False


def run_field_metadata_initialization():
    """运行字段元数据初始化"""
    logger.info("=== 运行字段元数据初始化 ===")

    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)

        # 检查是否存在重复数据（不进行清理，只是检查）
        with session_factory() as session:
            try:
                from collections import defaultdict

                # 查找重复的字段元数据记录
                duplicates = defaultdict(list)
                all_metadata = session.query(RuleFieldMetadata).all()

                for metadata in all_metadata:
                    key = (metadata.rule_key, metadata.field_name)
                    duplicates[key].append(metadata)

                # 统计重复记录
                duplicate_count = sum(1 for records in duplicates.values() if len(records) > 1)

                if duplicate_count > 0:
                    logger.warning(f"发现 {duplicate_count} 组重复的字段元数据记录")
                else:
                    logger.debug("没有发现重复的字段元数据记录")

            except Exception as e:
                logger.warning(f"检查重复数据时出错: {e}")
                # 不影响后续流程，继续执行

        # 执行增量初始化
        result = metadata_initializer.initialize_all_metadata(mode="incremental")

        logger.info(f"初始化结果: {result}")

        if result.get("total_errors", 0) == 0:
            logger.success("字段元数据初始化完成")
            return True
        else:
            # logger.error(f"字段元数据初始化存在错误: {result.get('errors', [])}")
            return False

    except Exception as e:
        logger.error(f"字段元数据初始化失败: {e}")
        return False


def verify_final_state():
    """验证最终状态"""
    logger.info("=== 验证最终状态 ===")

    try:
        session_factory = get_session_factory()
        with session_factory() as session:
            # 检查每个规则模板是否有对应的字段元数据
            templates = session.query(RuleTemplate).all()

            success_count = 0
            for template in templates:
                metadata_count = (
                    session.query(RuleFieldMetadata).filter(RuleFieldMetadata.rule_key == template.rule_key).count()
                )

                if metadata_count > 0:
                    success_count += 1
                    logger.debug(f"规则 {template.rule_key} 有 {metadata_count} 个字段元数据")
                else:
                    logger.warning(f"规则 {template.rule_key} 缺少字段元数据")

            logger.info(f"有字段元数据的规则模板: {success_count}/{len(templates)}")

            if success_count == len(templates) and len(templates) > 0:
                logger.success("所有规则模板都有对应的字段元数据")
                return True
            else:
                logger.error("部分规则模板缺少字段元数据")
                return False

    except Exception as e:
        logger.error(f"最终状态验证失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始验证主节点启动修复效果")

    checks = [
        ("字段映射配置", verify_field_mapping_config),
        ("规则文件", verify_rule_files),
        ("数据库表结构", verify_database_tables),
        ("规则变更检测", run_rule_change_detection),
        ("规则字段提取测试", test_rule_field_extraction),
        ("字段元数据初始化", run_field_metadata_initialization),
        ("最终状态", verify_final_state),
    ]

    passed = 0
    total = len(checks)

    for name, check_func in checks:
        logger.info(f"\n{'='*50}")
        logger.info(f"检查项目: {name}")
        logger.info(f"{'='*50}")

        try:
            if check_func():
                logger.success(f"✅ {name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {name} - 失败")
        except Exception as e:
            logger.error(f"❌ {name} - 异常: {e}")

    logger.info(f"\n{'='*50}")
    logger.info(f"验证结果: {passed}/{total} 项通过")
    logger.info(f"{'='*50}")

    if passed == total:
        logger.success("🎉 所有检查项目都通过，修复成功！")
        return 0
    else:
        logger.error("❌ 部分检查项目失败，需要进一步修复")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
简化的主节点启动测试脚本
专注于测试核心功能，避免复杂的数据清理逻辑
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.database import get_session_factory, RuleTemplate, RuleFieldMetadata
from services.rule_change_detector import RuleChangeDetector
from services.rule_field_metadata_initializer import RuleFieldMetadataInitializer
from tools.field_mapping_manager import FieldMappingManager
from loguru import logger


def test_field_mapping_config():
    """测试字段映射配置"""
    logger.info("=== 测试字段映射配置 ===")
    
    try:
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        
        # 检查配置版本
        version = field_mapping_manager.config.get("metadata", {}).get("version", "unknown")
        logger.info(f"字段映射配置版本: {version}")
        
        # 检查规则类型映射
        rule_type_mappings = field_mapping_manager.config.get("rule_type_mappings", {})
        logger.info(f"规则类型映射数量: {len(rule_type_mappings)}")
        
        return True
        
    except Exception as e:
        logger.error(f"字段映射配置测试失败: {e}")
        return False


def test_rule_change_detection():
    """测试规则变更检测"""
    logger.info("=== 测试规则变更检测 ===")
    
    try:
        session_factory = get_session_factory()
        with session_factory() as session:
            detector = RuleChangeDetector(db_session=session)
            detector.run()
        
        logger.success("规则变更检测完成")
        return True
        
    except Exception as e:
        logger.error(f"规则变更检测失败: {e}")
        return False


def test_field_extraction():
    """测试字段提取功能"""
    logger.info("=== 测试字段提取功能 ===")
    
    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)
        
        # 测试从规则类中提取字段映射
        extracted_mappings = metadata_initializer._extract_rule_mappings_from_classes()
        
        logger.info(f"从规则类中提取了 {len(extracted_mappings)} 个规则映射")
        
        # 显示前几个规则的详细信息
        count = 0
        for rule_key, mapping in extracted_mappings.items():
            if count >= 3:  # 只显示前3个
                break
            required_count = len(mapping.get('required_fields', []))
            optional_count = len(mapping.get('optional_fields', []))
            logger.info(f"规则 {rule_key}: 必填={required_count}, 可选={optional_count}")
            count += 1
        
        return len(extracted_mappings) > 0
        
    except Exception as e:
        logger.error(f"字段提取测试失败: {e}")
        return False


def test_metadata_initialization():
    """测试字段元数据初始化"""
    logger.info("=== 测试字段元数据初始化 ===")
    
    try:
        session_factory = get_session_factory()
        field_mapping_manager = FieldMappingManager("data/field_mapping.json")
        metadata_initializer = RuleFieldMetadataInitializer(session_factory, field_mapping_manager)
        
        # 执行增量初始化
        result = metadata_initializer.initialize_all_metadata(mode="incremental")
        
        logger.info(f"初始化结果: {result}")
        
        if result.get('total_errors', 0) == 0:
            logger.success("字段元数据初始化完成")
            return True
        else:
            logger.warning(f"字段元数据初始化存在 {result.get('total_errors', 0)} 个错误")
            for error in result.get('errors', [])[:5]:  # 只显示前5个错误
                logger.error(f"错误: {error}")
            return False
        
    except Exception as e:
        logger.error(f"字段元数据初始化失败: {e}")
        return False


def check_database_state():
    """检查数据库状态"""
    logger.info("=== 检查数据库状态 ===")
    
    try:
        session_factory = get_session_factory()
        with session_factory() as session:
            # 检查 rule_template 表
            template_count = session.query(RuleTemplate).count()
            logger.info(f"rule_template 表记录数: {template_count}")
            
            # 检查 rule_field_metadata 表
            metadata_count = session.query(RuleFieldMetadata).count()
            logger.info(f"rule_field_metadata 表记录数: {metadata_count}")
            
            # 检查每个规则模板是否有对应的字段元数据
            templates = session.query(RuleTemplate).all()
            templates_with_metadata = 0
            
            for template in templates:
                metadata_count_for_rule = session.query(RuleFieldMetadata).filter(
                    RuleFieldMetadata.rule_key == template.rule_key
                ).count()
                
                if metadata_count_for_rule > 0:
                    templates_with_metadata += 1
                    logger.debug(f"规则 {template.rule_key} 有 {metadata_count_for_rule} 个字段元数据")
                else:
                    logger.warning(f"规则 {template.rule_key} 缺少字段元数据")
            
            logger.info(f"有字段元数据的规则模板: {templates_with_metadata}/{len(templates)}")
            
            return templates_with_metadata > 0 and templates_with_metadata == len(templates)
        
    except Exception as e:
        logger.error(f"数据库状态检查失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始简化的主节点启动测试")
    
    tests = [
        ("字段映射配置", test_field_mapping_config),
        ("规则变更检测", test_rule_change_detection),
        ("字段提取功能", test_field_extraction),
        ("字段元数据初始化", test_metadata_initialization),
        ("数据库状态检查", check_database_state),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试项目: {name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.success(f"✅ {name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {name} - 失败")
        except Exception as e:
            logger.error(f"❌ {name} - 异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {passed}/{total} 项通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.success("🎉 所有测试都通过，主节点启动修复成功！")
        return 0
    elif passed >= total - 1:
        logger.warning("⚠️ 大部分测试通过，基本功能正常")
        return 0
    else:
        logger.error("❌ 多个测试失败，需要进一步修复")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
